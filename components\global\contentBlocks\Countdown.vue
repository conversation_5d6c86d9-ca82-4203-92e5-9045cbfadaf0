<script setup lang="ts">

const props = defineProps({
    content: {
        type: String,
        required: true,
    },
    meta: {
        type: Object,
        required: false,
    },
});

</script>

<template>
	<div class="bg-tda text-white text-center p-8 rounded-xl">
		<div v-html="content" v-auto-id></div>
		<CountdownTimer v-if="meta?.targetTime" :target-time="meta.targetTime" />
		<div v-html="meta?.afterContent" v-auto-id></div>
	</div>
</template>