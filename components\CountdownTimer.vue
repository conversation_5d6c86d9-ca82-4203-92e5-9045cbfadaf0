<script setup lang="ts">
import { useI18n } from "vue-i18n";

const props = defineProps({
  targetTime: {
    type: String,
    required: true,
  },
});

const { locale, t } = useI18n();

const count = ref(0);

const timeLeft = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
});

const isExpired = ref(false);

function incrementCount() {
  count.value++;
}

// Reactive computed for formatted time parts
const formattedTime = computed(() => {
  const rtf = new Intl.RelativeTimeFormat(locale.value);

  const daysFormatted = rtf.formatToParts(timeLeft.value.days, "days");
  const hoursFormatted = rtf.formatToParts(timeLeft.value.hours, "hours");
  const minutesFormatted = rtf.formatToParts(timeLeft.value.minutes, "minutes");
  const secondsFormatted = rtf.formatToParts(timeLeft.value.seconds, "seconds");

  return {
    days: daysFormatted[2]?.value?.trim() || "dní",
    hours: hoursFormatted[2]?.value?.trim() || "hodin",
    minutes: minutesFormatted[2]?.value?.trim() || "minut",
    seconds: secondsFormatted[2]?.value?.trim() || "sekund",
  };
});

function updateCountdown() {
  const now = new Date().getTime();
  const target = new Date(props.targetTime).getTime();
  const difference = target - now;

  console.log('Updating countdown:', { now, target, difference, targetTime: props.targetTime });

  if (difference > 0) {
    const newTimeLeft = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000),
    };
    timeLeft.value = newTimeLeft;
    isExpired.value = false;
    console.log('Time left:', newTimeLeft);
  } else {
    timeLeft.value = { days: 0, hours: 0, minutes: 0, seconds: 0 };
    isExpired.value = true;
    console.log('Time expired');
  }
}

let interval: NodeJS.Timeout | null = null;

// Initialize countdown immediately (works on server too)
updateCountdown();

// Update countdown every second, but only in browser
onMounted(() => {
  // Update immediately when mounted to ensure fresh data
  updateCountdown();
  interval = setInterval(updateCountdown, 1000);
});

// Cleanup interval on component unmount
onUnmounted(() => {
  if (interval) {
    clearInterval(interval);
  }
});

// Watch for locale changes and update formatting
watch(() => locale.value, () => {
  // Force reactivity update for formatted time
  updateCountdown();
});
</script>

<template>
  <div class="countdown-timer">
    <div class="mb-4 text-center">
      <p>Test reaktivity: {{ count }}</p>
      <button @click="incrementCount" class="bg-blue-500 text-white px-4 py-2 rounded">+1</button>
    </div>
    <div v-if="!isExpired" class="flex justify-center space-x-4 md:space-x-8 text-4xl font-bold">
      <div class="text-center">
        <div class="text-4xl md:text-6xl">{{ timeLeft.days }}</div>
        <div class="text-sm md:text-lg font-normal">{{ formattedTime.days }}</div>
      </div>
      <div class="text-center">
        <div class="text-4xl md:text-6xl">{{ timeLeft.hours }}</div>
        <div class="text-sm md:text-lg font-normal">{{ formattedTime.hours }}</div>
      </div>
      <div class="text-center">
        <div class="text-4xl md:text-6xl">{{ timeLeft.minutes }}</div>
        <div class="text-sm md:text-lg font-normal">{{ formattedTime.minutes }}</div>
      </div>
      <div class="text-center">
        <div class="text-4xl md:text-6xl">{{ timeLeft.seconds }}</div>
        <div class="text-sm md:text-lg font-normal">{{ formattedTime.seconds }}</div>
      </div>
    </div>
    <div v-else class="text-2xl font-bold">
      {{ t('countdown.expired') }}
    </div>
  </div>
</template>

<style scoped>
.countdown-timer {
  @apply my-6;
}
</style>
